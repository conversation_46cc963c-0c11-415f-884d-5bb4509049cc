import { Injectable, Logger } from '@nestjs/common';
import { RabbitSubscribe } from '@golevelup/nestjs-rabbitmq';
import {
  PRIVATE_MESSAGE_DLQ_EXCHANGE,
  PRIVATE_MESSAGE_DLQ_QUEUE,
  PRIVATE_MESSAGE_FAILED_ROUTING_KEY,
} from '../../../common/constants';
import { MailService } from '../../../core/mail/mail.service';
import { PrivateMessageDeliveryTaskPayload } from '../producers/private-message.producer';

@Injectable()
export class PrivateMessageDlqConsumer {
  private readonly logger = new Logger(PrivateMessageDlqConsumer.name);

  constructor(private readonly mailService: MailService) {}

  @RabbitSubscribe({
    exchange: PRIVATE_MESSAGE_DLQ_EXCHANGE,
    routingKey: PRIVATE_MESSAGE_FAILED_ROUTING_KEY,
    queue: PRIVATE_MESSAGE_DLQ_QUEUE,
    queueOptions: {
      durable: true,
    },
  })
  async handlePrivateMessageFailure(
    payload: PrivateMessageDeliveryTaskPayload,
  ) {
    this.logger.error(
      `Private message delivery failed permanently for message ${payload.messageId}`,
      {
        messageId: payload.messageId,
        senderId: payload.senderId,
        receiverId: payload.receiverId,
        failureReason: payload.failureReason || 'Unknown failure',
      },
    );
  }
}
