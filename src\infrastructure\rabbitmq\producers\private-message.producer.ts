import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { Injectable } from '@nestjs/common';
import {
  PRIVATE_MESSAGE_DELIVERY_ROUTING_KEY,
  MESSAGE_EXCHANGE,
} from '../../../common/constants';
import {
  MemberNotificationInfo,
  PushNotification,
} from '../../../infrastructure/notification/services/notification.service';

export interface PrivateMessageDeliveryTaskPayload {
  failureReason: string;
  messageId: number;
  senderId: number;
  receiverId: number;
  memberNotificationInfo: MemberNotificationInfo[];
  notificationPayload: PushNotification;
  retryCount?: number;
}

@Injectable()
export class PrivateMessageProducer {
  constructor(private readonly amqpConnection: AmqpConnection) {}

  async sendPrivateMessageDeliveryTask(
    payload: PrivateMessageDeliveryTaskPayload,
  ) {
    const message = {
      ...payload,
      createdAt: new Date(),
      retryCount: payload.retryCount || 0,
    };

    await this.amqpConnection.publish(
      MESSAGE_EXCHANGE,
      PRIVATE_MESSAGE_DELIVERY_ROUTING_KEY,
      message,
    );
  }
}
